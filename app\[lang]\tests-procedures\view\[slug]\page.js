import translationStrings from "../../language/translations";
import TestProcedureClient from "./TestProcedureClient";
const logger = require("../../../../utils/logger");

async function fetchTest(slug, lang) {
  try {
    // Use the current host and port instead of hardcoded localhost:3000
    const baseUrl =
      process.env.NEXT_PUBLIC_API_URL ||
      `http://localhost:${process.env.PORT || 3000}`;
    const response = await fetch(
      `${baseUrl}/api/tests-procedures/view/${slug}?lang=${lang}`,
      {
        method: "GET",
        headers: {
          "Accept-Language": lang,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch test");
    }
    return response.json();
  } catch (error) {
    logger.error("Error fetching test:", error);
    throw error;
  }
}

function extractFirstH1FromHtml(html) {
  if (!html) return null;
  const match = html.match(/<h1[^>]*>(.*?)<\/h1>/i);
  return match ? match[1].trim() : null;
}
function extractFirstPFromHtml(html) {
  if (!html) return null;
  const match = html.match(/<p[^>]*>(.*?)<\/p>/i);
  return match ? match[1].trim() : null;
}

export async function generateMetadata({ params }) {
  const { slug, lang } = await params;
  const language = lang || "en";
  const test = await fetchTest(slug, language);
  const firstHeading = extractFirstH1FromHtml(test.body_html);
  const firstPTag = extractFirstPFromHtml(test.body_html);
  return {
    title: firstHeading,
    description:
      test.meta_description || firstPTag.split(".")[0] + "." || "Default",
    openGraph: {
      title: firstHeading,
      description:
        test.meta_description || firstPTag.split(".")[0] + "." || "Default",
    },
  };
}

export default async function TestProcedurePage({ params }) {
  const { slug, lang } = params;
  const language = lang || "en";

  // Create a safe version of langStrings without functions
  const rawStrings = translationStrings[language] || translationStrings.en;
  const langStrings = {
    ...rawStrings,
    // Remove any functions to avoid serialization issues
    noTestsFound: undefined,
  };

  try {
    // Fetch test data server-side
    const test = await fetchTest(slug, language);
    const firstHeading = extractFirstH1FromHtml(test.body_html);
    return (
      <TestProcedureClient
        test={test}
        language={language}
        langStrings={langStrings}
        metaTitle={firstHeading}
        metaDescription={test.meta_description}
        firstHeading={firstHeading}
      />
    );
  } catch (error) {
    logger.error("Error in TestProcedurePage:", error);
    return (
      <TestProcedureClient
        error={error.message}
        language={language}
        langStrings={langStrings}
        metaTitle={""}
        metaDescription={""}
        firstHeading={""}
      />
    );
  }
}
