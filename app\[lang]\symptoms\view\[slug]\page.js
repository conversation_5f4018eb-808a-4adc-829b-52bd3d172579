import { getSymptomMetadata } from "../../../../api/symptoms/meta/getMetaData";
import translationStrings from "../../language/translations";
import SymptomViewClient from "./SymptomViewClient";
const logger = require("../../../../utils/logger");

async function fetchSymptom(slug, lang) {
  try {
    // Use the current host and port instead of hardcoded localhost:3000
    const baseUrl =
      process.env.NEXT_PUBLIC_API_URL ||
      `http://localhost:${process.env.PORT || 3000}`;
    const response = await fetch(
      `${baseUrl}/api/symptoms/view/${slug}?lang=${lang}`,
      {
        method: "GET",
        headers: {
          "Accept-Language": lang,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch symptom");
    }
    return response.json();
  } catch (error) {
    logger.error("Error fetching symptom:", error);
    throw error;
  }
}

function extractFirstH1FromHtml(html) {
  if (!html) return null;
  const match = html.match(/<h1[^>]*>(.*?)<\/h1>/i);
  return match ? match[1].trim() : null;
}
function extractFirstPFromHtml(html) {
  if (!html) return null;
  const match = html.match(/<p[^>]*>(.*?)<\/p>/i);
  return match ? match[1].trim() : null;
}
export async function generateMetadata({ params }) {
  const { slug, lang } = await params;
  const language = lang || "en";
  const symptom = await fetchSymptom(slug, language);
  const firstHeading = extractFirstH1FromHtml(symptom.body_html);
  const firstPTag = extractFirstPFromHtml(symptom.body_html);
  return {
    title: firstHeading || "Default title",
    description:
      symptom.meta_description ||
      firstPTag.split(".")[0] + "." ||
      "Default Description ",
    openGraph: {
      title: firstHeading || "Default title",
      description:
        symptom.meta_description ||
        firstPTag.split(".")[0] + "." ||
        "Default Description ",
    },
  };
}

export default async function SymptomPage({ params }) {
  const { slug, lang } = params;
  const language = lang || "en";

  // Create a safe version of langStrings without functions
  const rawStrings = translationStrings[language] || translationStrings.en;
  const langStrings = {
    ...rawStrings,
    // Remove any functions to avoid serialization issues
    noSymptomsFound: undefined,
    // Add any other functions that need to be removed
  };

  try {
    // Fetch symptom data server-side
    const symptom = await fetchSymptom(slug, language);
    const firstHeading = extractFirstH1FromHtml(symptom.body_html);
    return (
      <SymptomViewClient
        symptom={symptom}
        language={language}
        langStrings={langStrings}
        metaTitle={firstHeading}
        metaDescription={symptom.meta_description}
        firstHeading={firstHeading}
      />
    );
  } catch (error) {
    logger.error("Error in SymptomPage:", error);
    return (
      <SymptomViewClient
        error={error.message}
        language={language}
        langStrings={langStrings}
        metaTitle={""}
        metaDescription={""}
        firstHeading={""}
      />
    );
  }
}
