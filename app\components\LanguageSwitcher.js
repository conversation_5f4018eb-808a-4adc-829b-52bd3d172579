"use client";
import { useLanguage } from "../contexts/LanguageContext";
import { Select, MenuItem } from "@mui/material";
import { useRouter, usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { getRedirectPath } from "@/app/utils/getRedirectPath";
const logger = require("../utils/logger");

export default function LanguageSwitcher() {
  const { language, setLanguage } = useLanguage();
  const router = useRouter();
  const pathname = usePathname();
  const [languages, setLanguages] = useState([
    { code: language, name_in_local_script: language.toUpperCase() },
  ]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize with current language
  useEffect(() => {
    if (!languages.some((lang) => lang.code === language)) {
      setLanguages((prev) => [
        ...prev,
        { code: language, name_in_local_script: language.toUpperCase() },
      ]);
    }
  }, [language]);

  // Fetch available languages
  useEffect(() => {
    const fetchLanguages = async () => {
      try {
        setLoading(true);
        setError(null);
        // Use the current host and port instead of hardcoded localhost:3000
        const baseUrl =
          process.env.NEXT_PUBLIC_API_URL ||
          `http://localhost:${process.env.PORT || 3000}`;
        const response = await fetch(`${baseUrl}/api/languages`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch languages");
        }

        if (Array.isArray(data) && data.length > 0) {
          // Merge fetched languages with current language
          const updatedLanguages = [...data];
          if (!updatedLanguages.some((lang) => lang.code === language)) {
            updatedLanguages.push({
              code: language,
              name_in_local_script: language.toUpperCase(),
            });
          }
          updatedLanguages.sort((a, b) => {
            if (a.name_in_local_script < b.name_in_local_script) {
              return -1;
            }
            if (a.name_in_local_script > b.name_in_local_script) {
              return 1;
            }
            return 0;
          });
          setLanguages(updatedLanguages);
        }
      } catch (error) {
        logger.error("Error loading languages:", error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchLanguages();
  }, []);

  const handleLanguageChange = async (event) => {
    const newLang = event.target.value;
    setLanguage(newLang);

    // Update URL to reflect new language
    const isLibraryPath = pathname.includes("/library");
    const pathParts = pathname.split("/");
    const currentPath = isLibraryPath
      ? pathParts.slice(3).join("/")
      : pathParts.slice(2).join("/");
    const newPath = `/${newLang}${currentPath ? `/${currentPath}` : ""}`;

    try {
      await router.push(getRedirectPath(newPath), { scroll: false });
      // Force a hard navigation to ensure URL updates
      window.location.href = getRedirectPath(newPath);
    } catch (error) {
      logger.error("Navigation error:", error);
    }
  };

  // Always show the select, but disable it while loading
  const isDisabled = loading || error || !languages.length;

  return (
    <Select
      value={language}
      onChange={handleLanguageChange}
      size="small"
      disabled={isDisabled}
      MenuProps={{
        anchorOrigin: {
          vertical: "bottom",
          horizontal: "right",
        },
        transformOrigin: {
          vertical: "top",
          horizontal: "right",
        },
        slotProps: {
          paper: {
            sx: {
              mt: 1,
              maxHeight: "50vh",
              "& .MuiMenuItem-root": {
                py: 1,
              },
            },
          },
        },
      }}
      sx={{
        minWidth: { xs: 80, sm: 120 },
        "& .MuiSelect-select": {
          py: { xs: 1, sm: 1.5 },
          fontSize: { xs: "0.875rem", sm: "1rem" },
        },
      }}
    >
      {languages.map((lang) => (
        <MenuItem
          key={lang.code}
          value={lang.code}
          sx={{
            fontSize: { xs: "0.875rem", sm: "1rem" },
            py: { xs: 1, sm: 1.5 },
          }}
        >
          {loading ? "Loading..." : lang.name_in_local_script}
        </MenuItem>
      ))}
    </Select>
  );
}
